# LocationView Watermark Implementation với GPUVideo-android

## Tổng quan
Implementation này sử dụng thư viện GPUVideo-android để thêm watermark LocationView vào video sau khi quay, với vị trí bottom-center theo yêu cầu. Toàn bộ implementation tuân theo kiến trúc MVVM với UI States.

## Các thành phần chính

### 1. UI State Management
- **CameraWatermarkUiState**: Quản lý trạng thái watermark
  - `isWatermarkEnabled`: Bật/tắt watermark
  - `isProcessingWatermark`: Trạng thái đang xử lý
  - `watermarkPosition`: Vị trí watermark (mặc định BOTTOM_CENTER)
  - `watermarkAlpha`: Độ trong suốt

### 2. Custom OpenGL Filter
- **GlLocationWatermarkFilter**: Custom filter extends GlOverlayFilter
  - Vẽ LocationView bitmap lên video frame
  - Hỗ trợ nhiều vị trí: BOTTOM_CENTER, BOTTOM_LEFT, BOTTOM_RIGHT, etc.
  - Tự động scale và maintain aspect ratio
  - Hỗ trợ alpha transparency

### 3. Video Processing
- **VideoWatermarkProcessor**: Utility class xử lý video
  - Sử dụng GPUMp4Composer để apply watermark
  - Hỗ trợ xử lý single video và multiple video parts
  - Async processing với progress callback
  - Error handling và resource management

### 4. ViewModel Integration
- **CameraViewModel**: Thêm các methods quản lý watermark
  - `setWatermarkEnabled()`: Bật/tắt watermark
  - `setWatermarkPosition()`: Đặt vị trí watermark
  - `setWatermarkAlpha()`: Đặt độ trong suốt
  - `processVideoWithWatermark()`: Xử lý video với watermark
  - `processMultipleVideosWithWatermark()`: Xử lý nhiều video parts

### 5. Fragment Integration
- **CameraFragmentEx**: Cập nhật logic xử lý video
  - Tạo bitmap từ LocationView (clone without edit button)
  - Gọi watermark processing sau khi recording
  - Observer cho watermark processing state
  - Loading indicator khi đang xử lý

## Workflow

### 1. Video Recording
1. User bắt đầu quay video
2. LocationView hiển thị thông tin location real-time
3. Video được lưu thành các parts (nếu có pause/resume)

### 2. Video Processing với Watermark
1. Sau khi stop recording, `onSaveVideoEvent()` được gọi
2. Tạo bitmap từ LocationView hiện tại (clone without edit button)
3. Gọi `processMultipleVideosWithWatermark()` với:
   - Input video paths
   - Location bitmap
   - Watermark position (BOTTOM_CENTER)
   - Progress callback
4. GPUMp4Composer xử lý video với GlLocationWatermarkFilter
5. Watermark được vẽ ở vị trí bottom-center với proper scaling

### 3. Result Handling
1. Hiển thị progress indicator khi đang xử lý
2. Sau khi hoàn thành, hiển thị CameraResultDialog
3. User có thể preview và save video đã có watermark

## Cấu hình Watermark

### Vị trí (WatermarkPosition)
- `BOTTOM_CENTER`: Giữa dưới (mặc định)
- `BOTTOM_LEFT`: Trái dưới
- `BOTTOM_RIGHT`: Phải dưới
- `TOP_CENTER`: Giữa trên
- `TOP_LEFT`: Trái trên
- `TOP_RIGHT`: Phải trên

### Kích thước và Margin
- Watermark width: 80% screen width
- Margin: 5% từ các cạnh
- Tự động maintain aspect ratio của LocationView

### Transparency
- Alpha range: 0.0 - 1.0
- Mặc định: 1.0 (opaque)

## Dependencies
- GPUVideo-android library (module `:gpuv`)
- OpenGL ES 2.0
- Android MediaMetadataRetriever

## Testing
- **WatermarkTestActivity**: Test activity cho development
- Test watermark filter functionality
- Test video processor capabilities

## Performance Considerations
- Video processing chạy trên background thread
- Progress callback để update UI
- Proper resource management (bitmap recycling)
- Error handling cho memory issues

## Error Handling
- Validation input video files
- Check bitmap availability
- Handle OpenGL context issues
- Graceful fallback nếu watermark processing fails

## Future Enhancements
- Thêm animation effects cho watermark
- Hỗ trợ multiple watermarks
- Custom watermark styling options
- Batch processing optimization
