package co.cameralocation.framework.presentation.camera

import android.Manifest
import android.graphics.Color
import android.view.View
import androidx.core.view.isVisible
import co.cameralocation.R
import com.otaliastudios.cameraview.CameraView
import dagger.hilt.android.AndroidEntryPoint
import co.cameralocation.customview.locationinfo.BaseLocationInfoEditableLayoutView
import co.cameralocation.databinding.FragmentCameraBinding
import co.cameralocation.framework.manager.LocationManager
import co.cameralocation.framework.presentation.common.BaseFragment
import co.cameralocation.framework.presentation.model.camera.CameraGridMode
import co.cameralocation.util.getBitmapFromDrawableRes
import co.cameralocation.util.textToBitmap
import java.util.concurrent.atomic.AtomicInteger
import javax.inject.Inject

@AndroidEntryPoint
class CameraFragment : BaseFragment<FragmentCameraBinding, CameraViewModel>(
    FragmentCameraBinding::inflate,
    CameraViewModel::class.java
) {
    @Inject
    lateinit var locationManager: LocationManager

    var isRequestSaveFullVideoFileAfterSavingPartDone = false
    var partIndex = AtomicInteger(0)
    var cameraView: CameraView? = null

    var currentLocationView: BaseLocationInfoEditableLayoutView<*>? = null

    val permissions = listOf(
        Manifest.permission.CAMERA,
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )

    val gridIcons by lazy {
        hashMapOf(
            CameraGridMode.OFF to getBitmapFromDrawableRes(R.drawable.ic_grid_camera_screen, requireContext()),
            CameraGridMode.DRAW_3X3 to textToBitmap("3x3", textSize = resources.getDimensionPixelSize(htkien.autodimens.R.dimen._16sp).toFloat(), textColor = Color.WHITE),
            CameraGridMode.DRAW_4X4 to textToBitmap("4x4", textSize = resources.getDimensionPixelSize(htkien.autodimens.R.dimen._16sp).toFloat(), textColor = Color.WHITE),
            CameraGridMode.GOLDEN_RATIO to textToBitmap("GR", textSize = resources.getDimensionPixelSize(htkien.autodimens.R.dimen._16sp).toFloat(), textColor = Color.WHITE),
        )
    }

    val timerIcons by lazy {
        hashMapOf(
            0 to getBitmapFromDrawableRes(R.drawable.ic_time_camera_screen, requireContext()),
            3 to textToBitmap("3s", textSize = resources.getDimensionPixelSize(htkien.autodimens.R.dimen._16sp).toFloat(), textColor = Color.WHITE),
            5 to textToBitmap("5s", textSize = resources.getDimensionPixelSize(htkien.autodimens.R.dimen._16sp).toFloat(), textColor = Color.WHITE),
            10 to textToBitmap("10s", textSize = resources.getDimensionPixelSize(htkien.autodimens.R.dimen._16sp).toFloat(), textColor = Color.WHITE),
        )
    }

    override fun init(view: View) {

        //TODO: BA request hide settings ver 1.0.0
        binding.btnSettings.isVisible = false
        setUpCurrentLocation()
        loadCameraSettingsPreferences()
        setupBackButton()
        setUpCameraView()
        setupGridButton()
        setupCaptureButton()
        setupSwitchCameraButton()
        setupFlashButton()
        setupRatioSpinner()
        setupSwitchModeButton()
        setupSettingsButton()
        setupTimerButton()
        setupPauseResumeButton()
        setupButtonSaveMediaEvent()
    }

    override fun subscribeObserver(view: View) {
        observeCameraMode()
        observeCameraFacing()
        observeFlashMode()
        observeGridMode()
        observeAspectRatio()
        observeRecordingState()
        observeTimerState()
        observeTimeButtonVisibilityChanged()
        observeRecordingTimerVisibilityChanged()
        observeSavingStateChanged()
        observePictureCountdownTimerTextVisibilityChanged()
        observeCurrentLocationItem()
        observePauseResumeButtonVisibilityChanged()
        observeTimerCountdownSecondsChanged()
        observeSwitchFacingButtonVisibilityChanged()
        observeGoToSaveMediaButtonVisibilityChanged()
        observeToggleBetweenPictureAndVideoModeChanged()
        observeEditButtonVisibilityChanged()
        observeOrientationChanged()
    }

    companion object {
        const val TAG = "CameraFragment"
    }
}

