package co.cameralocation.framework.presentation.model.editmediaphoto

enum class EditImageType {
    R<PERSON><PERSON>CE, CROP, FLIP, ADJUS<PERSON>, FILTER,
    B<PERSON><PERSON>ING_NONE, B<PERSON><PERSON>ING_ADD, B<PERSON><PERSON>ING_DARKEN,
    <PERSON><PERSON><PERSON>ING_MULTIPLY, <PERSON><PERSON><PERSON><PERSON>_LIGHTEN, <PERSON><PERSON><PERSON><PERSON>_SCREEN,
    <PERSON><PERSON><PERSON><PERSON>_OVERLAY,
    ADD_SI, OPACITY_SI, B<PERSON>NDING_SI, WARP_SI, ERASER_SI, DELETE_SI,
    ADD_ST, EDIT_ST, FONT_ST, COLOR_ST, STROKE_ST, DELETE_ST,
    OPACITY_ST, STYLE_ST, WARP_ST, SHADOW_ST,
    FORMAT_ST, SIZE_ST, BLENDING_ST,
    RESET_SHADOW_ST, POSITION_SHADOW_ST, COLOR_SHADOW_ST,
    BLUR_SHADOW_ST
}