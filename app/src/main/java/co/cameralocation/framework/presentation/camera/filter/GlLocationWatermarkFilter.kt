package co.cameralocation.framework.presentation.camera.filter

import android.graphics.Bitmap
import android.graphics.Canvas
import com.daasuu.gpuv.egl.filter.GlOverlayFilter
import androidx.core.graphics.scale

/**
 * Custom OpenGL filter for rendering LocationView as watermark on video
 * Extends GlOverlayFilter to provide location-specific watermark functionality
 */
class GlLocationWatermarkFilter(
    private var locationBitmap: Bitmap?,
    private var alpha: Float = 1.0f
) : GlOverlayFilter() {

    companion object {
        private const val DEFAULT_MARGIN_RATIO = 0.05f // 5% margin from edges
        private const val DEFAULT_WIDTH_RATIO = 0.8f   // 80% of screen width
    }

    /**
     * Update the location bitmap for watermark
     */
    fun updateLocationBitmap(bitmap: Bitmap?) {
        this.locationBitmap = bitmap
    }


    /**
     * Update watermark alpha transparency
     */
    fun updateAlpha(newAlpha: Float) {
        this.alpha = newAlpha.coerceIn(0f, 1f)
    }

    /**
     * Main drawing method called by OpenGL renderer
     * Draws the location bitmap on the canvas at specified position
     */
    override fun drawCanvas(canvas: Canvas) {
        val bitmap = locationBitmap
        if (bitmap == null || bitmap.isRecycled) {
            return
        }

        // Calculate canvas dimensions
        val canvasWidth = canvas.width.toFloat()
        val canvasHeight = canvas.height.toFloat()

        // Calculate watermark dimensions maintaining aspect ratio
        val bitmapAspectRatio = bitmap.width.toFloat() / bitmap.height.toFloat()
        val watermarkWidth = canvasWidth * DEFAULT_WIDTH_RATIO
        val watermarkHeight = watermarkWidth / bitmapAspectRatio

        // Calculate margins
        val marginX = canvasWidth * DEFAULT_MARGIN_RATIO
        val marginY = canvasHeight * DEFAULT_MARGIN_RATIO

        // Calculate position based on WatermarkPosition
        val (x, y) = calculatePosition(
            canvasWidth = canvasWidth,
            canvasHeight = canvasHeight,
            watermarkWidth = watermarkWidth,
            watermarkHeight = watermarkHeight,
            marginX = marginX,
            marginY = marginY
        )

        // Create scaled bitmap if needed
        val scaledBitmap = if (watermarkWidth.toInt() != bitmap.width || watermarkHeight.toInt() != bitmap.height) {
            bitmap.scale(watermarkWidth.toInt(), watermarkHeight.toInt())
        } else {
            bitmap
        }

        // Apply alpha if needed
        val paint = if (alpha < 1.0f) {
            android.graphics.Paint().apply {
                this.alpha = (alpha * 255).toInt()
            }
        } else {
            null
        }

        // Draw the watermark
        canvas.drawBitmap(scaledBitmap, x, y, paint)

        // Clean up scaled bitmap if it was created
        if (scaledBitmap != bitmap) {
            scaledBitmap.recycle()
        }
    }

    /**
     * Calculate the position coordinates based on WatermarkPosition enum
     */
    private fun calculatePosition(
        canvasWidth: Float,
        canvasHeight: Float,
        watermarkWidth: Float,
        watermarkHeight: Float,
        marginX: Float,
        marginY: Float
    ): Pair<Float, Float> {
        val x = (canvasWidth - watermarkWidth) / 2f
        val y = canvasHeight - watermarkHeight - marginY
        return Pair(x, y)
    }

    /**
     * Check if watermark is ready to be rendered
     */
    fun isWatermarkReady(): Boolean {
        return locationBitmap != null && !locationBitmap!!.isRecycled
    }

    /**
     * Release resources
     */
    override fun release() {
        locationBitmap?.let { bitmap ->
            if (!bitmap.isRecycled) {
                bitmap.recycle()
            }
        }
        locationBitmap = null
    }
}
