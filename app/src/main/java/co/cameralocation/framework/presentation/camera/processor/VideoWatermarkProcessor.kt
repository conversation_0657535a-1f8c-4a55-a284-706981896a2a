package co.cameralocation.framework.presentation.camera.processor

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import android.util.Size
import com.daasuu.gpuv.composer.GPUMp4Composer
import com.daasuu.gpuv.egl.filter.GlFilter
import co.cameralocation.framework.presentation.camera.filter.GlLocationWatermarkFilter
import co.cameralocation.framework.presentation.model.camera.WatermarkPosition
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * Utility class for processing video with LocationView watermark using GPUVideo library
 * Handles video composition with watermark overlay positioned at bottom-center
 */
class VideoWatermarkProcessor(private val context: Context) {

    companion object {
        private const val TAG = "VideoWatermarkProcessor"
        private const val DEFAULT_BITRATE = 10_000_000 // 10 Mbps
        private const val DEFAULT_WATERMARK_ALPHA = 1.0f
    }

    /**
     * Process video with LocationView watermark
     * 
     * @param inputVideoPath Path to input video file
     * @param outputVideoPath Path for output video file
     * @param locationBitmap Bitmap of LocationView to use as watermark
     * @param watermarkPosition Position for watermark (default: BOTTOM_CENTER)
     * @param watermarkAlpha Alpha transparency for watermark (0.0 - 1.0)
     * @param onProgress Progress callback (0.0 - 1.0)
     * @param onSuccess Success callback with output file path
     * @param onError Error callback with exception
     */
    suspend fun processVideoWithWatermark(
        inputVideoPath: String,
        outputVideoPath: String,
        locationBitmap: Bitmap?,
        watermarkPosition: WatermarkPosition = WatermarkPosition.BOTTOM_CENTER,
        watermarkAlpha: Float = DEFAULT_WATERMARK_ALPHA,
        onProgress: ((Float) -> Unit)? = null,
        onSuccess: (String) -> Unit,
        onError: (Exception) -> Unit
    ) = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting video watermark processing...")
            Log.d(TAG, "Input: $inputVideoPath")
            Log.d(TAG, "Output: $outputVideoPath")
            
            // Validate input
            val inputFile = File(inputVideoPath)
            if (!inputFile.exists()) {
                throw IllegalArgumentException("Input video file does not exist: $inputVideoPath")
            }

            if (locationBitmap == null || locationBitmap.isRecycled()) {
                throw IllegalArgumentException("Location bitmap is null or recycled")
            }

            // Create watermark filter
            val watermarkFilter = GlLocationWatermarkFilter(
                locationBitmap = locationBitmap,
                position = watermarkPosition,
                alpha = watermarkAlpha
            )

            // Process video with watermark
            val result = processVideo(
                inputPath = inputVideoPath,
                outputPath = outputVideoPath,
                filter = watermarkFilter,
                onProgress = onProgress
            )

            if (result) {
                Log.d(TAG, "Video watermark processing completed successfully")
                withContext(Dispatchers.Main) {
                    onSuccess(outputVideoPath)
                }
            } else {
                throw Exception("Video processing failed")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error processing video with watermark", e)
            withContext(Dispatchers.Main) {
                onError(e)
            }
        }
    }

    /**
     * Internal method to process video using GPUMp4Composer
     */
    private suspend fun processVideo(
        inputPath: String,
        outputPath: String,
        filter: GlFilter,
        onProgress: ((Float) -> Unit)?
    ): Boolean = suspendCoroutine { continuation ->
        
        try {
            val composer = GPUMp4Composer(context, inputPath, outputPath)
                .filter(filter)
                .mute(false) // Keep audio
                .listener(object : GPUMp4Composer.Listener {
                    override fun onProgress(progress: Double) {
                        Log.d(TAG, "Processing progress: ${(progress * 100).toInt()}%")
                        onProgress?.invoke(progress.toFloat())
                    }

                    override fun onCompleted() {
                        Log.d(TAG, "Video processing completed")
                        continuation.resume(true)
                    }

                    override fun onCanceled() {
                        Log.w(TAG, "Video processing canceled")
                        continuation.resume(false)
                    }

                    override fun onFailed(exception: Exception) {
                        Log.e(TAG, "Video processing failed", exception)
                        continuation.resume(false)
                    }
                })

            // Start processing
            composer.start()

        } catch (e: Exception) {
            Log.e(TAG, "Error starting video composition", e)
            continuation.resume(false)
        }
    }

    /**
     * Process multiple video parts with watermark and merge them
     * 
     * @param inputVideoPaths List of input video file paths
     * @param outputVideoPath Path for final merged output video
     * @param locationBitmap Bitmap of LocationView to use as watermark
     * @param watermarkPosition Position for watermark
     * @param watermarkAlpha Alpha transparency for watermark
     * @param onProgress Progress callback
     * @param onSuccess Success callback
     * @param onError Error callback
     */
    suspend fun processMultipleVideosWithWatermark(
        inputVideoPaths: List<String>,
        outputVideoPath: String,
        locationBitmap: Bitmap?,
        watermarkPosition: WatermarkPosition = WatermarkPosition.BOTTOM_CENTER,
        watermarkAlpha: Float = DEFAULT_WATERMARK_ALPHA,
        onProgress: ((Float) -> Unit)? = null,
        onSuccess: (String) -> Unit,
        onError: (Exception) -> Unit
    ) = withContext(Dispatchers.IO) {
        try {
            if (inputVideoPaths.isEmpty()) {
                throw IllegalArgumentException("No input videos provided")
            }

            if (inputVideoPaths.size == 1) {
                // Single video, process directly
                processVideoWithWatermark(
                    inputVideoPath = inputVideoPaths.first(),
                    outputVideoPath = outputVideoPath,
                    locationBitmap = locationBitmap,
                    watermarkPosition = watermarkPosition,
                    watermarkAlpha = watermarkAlpha,
                    onProgress = onProgress,
                    onSuccess = onSuccess,
                    onError = onError
                )
                return@withContext
            }

            // Multiple videos - process each with watermark then merge
            val tempDir = File(context.cacheDir, "watermark_temp")
            if (!tempDir.exists()) {
                tempDir.mkdirs()
            }

            val processedVideos = mutableListOf<String>()
            val totalVideos = inputVideoPaths.size

            // Process each video with watermark
            inputVideoPaths.forEachIndexed { index, inputPath ->
                val tempOutputPath = File(tempDir, "watermarked_$index.mp4").absolutePath
                
                val success = processVideo(
                    inputPath = inputPath,
                    outputPath = tempOutputPath,
                    filter = GlLocationWatermarkFilter(
                        locationBitmap = locationBitmap,
                        position = watermarkPosition,
                        alpha = watermarkAlpha
                    ),
                    onProgress = { progress ->
                        val overallProgress = (index + progress) / totalVideos
                        onProgress?.invoke(overallProgress)
                    }
                )

                if (success) {
                    processedVideos.add(tempOutputPath)
                } else {
                    throw Exception("Failed to process video part $index")
                }
            }

            // TODO: Implement video merging logic here if needed
            // For now, if multiple videos, we'll use the first processed one
            if (processedVideos.isNotEmpty()) {
                val firstProcessed = File(processedVideos.first())
                val outputFile = File(outputVideoPath)
                firstProcessed.copyTo(outputFile, overwrite = true)
                
                // Clean up temp files
                processedVideos.forEach { tempPath ->
                    File(tempPath).delete()
                }
                tempDir.delete()

                withContext(Dispatchers.Main) {
                    onSuccess(outputVideoPath)
                }
            } else {
                throw Exception("No videos were processed successfully")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error processing multiple videos with watermark", e)
            withContext(Dispatchers.Main) {
                onError(e)
            }
        }
    }

    /**
     * Check if video processing is supported for the given file
     */
    fun isVideoProcessingSupported(videoPath: String): Boolean {
        val file = File(videoPath)
        return file.exists() && file.length() > 0 && 
               (videoPath.endsWith(".mp4", ignoreCase = true) || 
                videoPath.endsWith(".mov", ignoreCase = true))
    }
}
