package co.cameralocation.framework.presentation.model.camera

import android.graphics.Bitmap
import com.otaliastudios.cameraview.controls.Facing
import com.otaliastudios.cameraview.controls.Flash
import com.otaliastudios.cameraview.controls.Mode
import com.otaliastudios.cameraview.size.AspectRatio
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfo

data class CameraUiState(
    val isTakingPicture: Boolean = false,
    val isTakingVideo: Boolean = false,
    val isSaving: Boolean = false,
    val cameraSettingUiState: CameraSettingUiState = CameraSettingUiState(),
    val cameraPictureCountdownUiState: CameraPictureCountdownUiState = CameraPictureCountdownUiState(),
    val cameraVideoRecorderUiState: CameraVideoRecorderUiState = CameraVideoRecorderUiState(),
    val cameraLocationViewsUiState: CameraLocationViewsUiState = CameraLocationViewsUiState()
)

data class CameraSettingUiState(
    val flashMode: Flash = Flash.OFF,
    val cameraFacing: Facing = Facing.BACK,
    val cameraMode: Mode = Mode.PICTURE,
    val gridMode: CameraGridMode = CameraGridMode.OFF,
    val aspectRatio: AspectRatio = AspectRatio.of(1, 1),
    val deviceOrientation: Int = 0,
)

data class CameraVideoRecorderUiState(
    val isRecordingSessionRunning: Boolean = false,
    val isRecordingPaused: Boolean = false,
    val recordingTimeSeconds: Int = 0
)

data class CameraPictureCountdownUiState(
    val timerEnabled: Boolean = false,
    val timerSeconds: Int = 0,
    val timerCountdown: Int = 0,
    val isTimerRunning: Boolean = false,
)

fun CameraUiState.isShowingEditLocationButton(): Boolean {
    return !isTakingPicture && !isTakingVideo
}

fun CameraUiState.isTimeButtonCompletelyVisible(): Boolean {
    return cameraSettingUiState.cameraMode == Mode.PICTURE
}

fun CameraUiState.isRecordingTimerCompletelyVisible(): Boolean {
    return cameraSettingUiState.cameraMode == Mode.VIDEO && cameraVideoRecorderUiState.isRecordingSessionRunning
}

fun CameraUiState.isPictureCountdownTimerCompletelyVisible(): Boolean {
    return cameraSettingUiState.cameraMode == Mode.PICTURE && cameraPictureCountdownUiState.timerEnabled && cameraPictureCountdownUiState.timerCountdown != 0
}

fun CameraUiState.isShowingToggleBetweenPictureAndVideoModeButton(): Boolean {
    return !isShowingPauseResumeButton()
}

fun CameraUiState.isShowingPauseResumeButton(): Boolean {
    return isRecordingTimerCompletelyVisible()
}

fun CameraUiState.isShowingSwitchFacingButton(): Boolean {
    return !isShowingPauseResumeButton()
}

fun CameraUiState.isShowingGoToSaveMediaButton(): Boolean {
    return !isShowingPauseResumeButton()
}