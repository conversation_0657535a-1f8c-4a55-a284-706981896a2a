package co.cameralocation.framework.presentation.camera.renderer

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.SurfaceTexture
import android.opengl.GLES20
import android.opengl.GLUtils
import android.util.Log
import androidx.annotation.NonNull
import com.otaliastudios.cameraview.filter.Filter
import com.otaliastudios.cameraview.preview.RendererFrameCallback
import com.otaliastudios.cameraview.preview.RendererThread
import co.cameralocation.framework.presentation.model.camera.LocationViewRenderingPosition
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer
import javax.microedition.khronos.opengles.GL10

/**
 * OpenGL renderer for LocationView that renders location overlay on each frame
 * Always positions at BOTTOM and CENTER_HORIZONTAL regardless of device orientation
 */
class LocationViewRenderer : RendererFrameCallback {

    companion object {
        private const val TAG = "LocationViewRenderer"
        
        // Vertex shader for texture rendering
        private const val VERTEX_SHADER = """
            attribute vec4 aPosition;
            attribute vec2 aTextureCoord;
            varying vec2 vTextureCoord;
            void main() {
                gl_Position = aPosition;
                vTextureCoord = aTextureCoord;
            }
        """
        
        // Fragment shader for texture rendering
        private const val FRAGMENT_SHADER = """
            precision mediump float;
            varying vec2 vTextureCoord;
            uniform sampler2D uTexture;
            uniform float uAlpha;
            void main() {
                vec4 color = texture2D(uTexture, vTextureCoord);
                gl_FragColor = vec4(color.rgb, color.a * uAlpha);
            }
        """
    }

    private var locationViewBitmap: Bitmap? = null
    private var renderingPosition: LocationViewRenderingPosition = LocationViewRenderingPosition.BOTTOM_CENTER
    private var isVisible: Boolean = true
    
    // OpenGL components
    private var shaderProgram: Int = 0
    private var textureId: Int = 0
    private var vertexBuffer: FloatBuffer? = null
    private var textureBuffer: FloatBuffer? = null
    
    // Shader attribute/uniform locations
    private var positionHandle: Int = 0
    private var textureCoordHandle: Int = 0
    private var textureHandle: Int = 0
    private var alphaHandle: Int = 0
    
    // Rendering properties
    private var viewportWidth: Int = 0
    private var viewportHeight: Int = 0
    private var alpha: Float = 1.0f

    /**
     * Update the location view bitmap to be rendered
     */
    fun updateLocationViewBitmap(bitmap: Bitmap?) {
        this.locationViewBitmap = bitmap
        if (bitmap != null && textureId != 0) {
            updateTexture(bitmap)
        }
    }

    /**
     * Set the rendering position (always BOTTOM_CENTER for this implementation)
     */
    fun setRenderingPosition(position: LocationViewRenderingPosition) {
        this.renderingPosition = position
    }

    /**
     * Set visibility of the location view overlay
     */
    fun setVisible(visible: Boolean) {
        this.isVisible = visible
    }

    /**
     * Set alpha transparency for the overlay
     */
    fun setAlpha(alpha: Float) {
        this.alpha = alpha.coerceIn(0f, 1f)
    }

    @RendererThread
    override fun onRendererTextureCreated(textureId: Int) {
        Log.d(TAG, "onRendererTextureCreated: $textureId")
        initializeOpenGL()
        locationViewBitmap?.let { bitmap ->
            updateTexture(bitmap)
        }
    }

    @RendererThread
    override fun onRendererFilterChanged(@NonNull filter: Filter) {
        // No specific handling needed for filter changes
        Log.d(TAG, "onRendererFilterChanged: ${filter.javaClass.simpleName}")
    }

    @RendererThread
    override fun onRendererFrame(
        @NonNull surfaceTexture: SurfaceTexture,
        rotation: Int,
        scaleX: Float,
        scaleY: Float
    ) {
        if (!isVisible || locationViewBitmap == null || shaderProgram == 0) {
            return
        }

        // Get current viewport dimensions
        val viewport = IntArray(4)
        GLES20.glGetIntegerv(GLES20.GL_VIEWPORT, viewport, 0)
        viewportWidth = viewport[2]
        viewportHeight = viewport[3]

        renderLocationView()
    }

    @RendererThread
    private fun initializeOpenGL() {
        // Create shader program
        shaderProgram = createShaderProgram(VERTEX_SHADER, FRAGMENT_SHADER)
        if (shaderProgram == 0) {
            Log.e(TAG, "Failed to create shader program")
            return
        }

        // Get attribute and uniform locations
        positionHandle = GLES20.glGetAttribLocation(shaderProgram, "aPosition")
        textureCoordHandle = GLES20.glGetAttribLocation(shaderProgram, "aTextureCoord")
        textureHandle = GLES20.glGetUniformLocation(shaderProgram, "uTexture")
        alphaHandle = GLES20.glGetUniformLocation(shaderProgram, "uAlpha")

        // Create vertex buffer for quad
        val vertices = floatArrayOf(
            -1.0f, -1.0f, 0.0f,  // Bottom left
             1.0f, -1.0f, 0.0f,  // Bottom right
            -1.0f,  1.0f, 0.0f,  // Top left
             1.0f,  1.0f, 0.0f   // Top right
        )
        
        vertexBuffer = ByteBuffer.allocateDirect(vertices.size * 4)
            .order(ByteOrder.nativeOrder())
            .asFloatBuffer()
            .apply {
                put(vertices)
                position(0)
            }

        // Create texture coordinate buffer
        val textureCoords = floatArrayOf(
            0.0f, 1.0f,  // Bottom left
            1.0f, 1.0f,  // Bottom right
            0.0f, 0.0f,  // Top left
            1.0f, 0.0f   // Top right
        )
        
        textureBuffer = ByteBuffer.allocateDirect(textureCoords.size * 4)
            .order(ByteOrder.nativeOrder())
            .asFloatBuffer()
            .apply {
                put(textureCoords)
                position(0)
            }

        // Generate texture
        val textures = IntArray(1)
        GLES20.glGenTextures(1, textures, 0)
        textureId = textures[0]
        
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, textureId)
        GLES20.glTexParameteri(GLES20.GL_TEXTURE_2D, GLES20.GL_TEXTURE_MIN_FILTER, GLES20.GL_LINEAR)
        GLES20.glTexParameteri(GLES20.GL_TEXTURE_2D, GLES20.GL_TEXTURE_MAG_FILTER, GLES20.GL_LINEAR)
        GLES20.glTexParameteri(GLES20.GL_TEXTURE_2D, GLES20.GL_TEXTURE_WRAP_S, GLES20.GL_CLAMP_TO_EDGE)
        GLES20.glTexParameteri(GLES20.GL_TEXTURE_2D, GLES20.GL_TEXTURE_WRAP_T, GLES20.GL_CLAMP_TO_EDGE)
    }

    @RendererThread
    private fun updateTexture(bitmap: Bitmap) {
        if (textureId == 0) return
        
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, textureId)
        GLUtils.texImage2D(GLES20.GL_TEXTURE_2D, 0, bitmap, 0)
        
        Log.d(TAG, "Updated texture with bitmap: ${bitmap.width}x${bitmap.height}")
    }

    @RendererThread
    private fun renderLocationView() {
        val bitmap = locationViewBitmap ?: return
        
        // Enable blending for transparency
        GLES20.glEnable(GLES20.GL_BLEND)
        GLES20.glBlendFunc(GLES20.GL_SRC_ALPHA, GLES20.GL_ONE_MINUS_SRC_ALPHA)
        
        // Use shader program
        GLES20.glUseProgram(shaderProgram)
        
        // Calculate position for BOTTOM_CENTER
        val bitmapAspectRatio = bitmap.width.toFloat() / bitmap.height.toFloat()
        val viewportAspectRatio = viewportWidth.toFloat() / viewportHeight.toFloat()
        
        // Calculate size to maintain aspect ratio
        val overlayWidth: Float
        val overlayHeight: Float
        
        // Scale to fit width with padding
        val maxWidth = 0.8f // 80% of screen width
        overlayWidth = maxWidth
        overlayHeight = overlayWidth / bitmapAspectRatio
        
        // Position at bottom center
        val left = -overlayWidth / 2f
        val right = overlayWidth / 2f
        val bottom = -0.9f // Near bottom of screen
        val top = bottom + overlayHeight
        
        // Update vertex positions
        val positionVertices = floatArrayOf(
            left, bottom, 0.0f,   // Bottom left
            right, bottom, 0.0f,  // Bottom right
            left, top, 0.0f,      // Top left
            right, top, 0.0f      // Top right
        )
        
        vertexBuffer?.apply {
            clear()
            put(positionVertices)
            position(0)
        }
        
        // Set vertex attributes
        GLES20.glEnableVertexAttribArray(positionHandle)
        GLES20.glVertexAttribPointer(positionHandle, 3, GLES20.GL_FLOAT, false, 0, vertexBuffer)
        
        GLES20.glEnableVertexAttribArray(textureCoordHandle)
        GLES20.glVertexAttribPointer(textureCoordHandle, 2, GLES20.GL_FLOAT, false, 0, textureBuffer)
        
        // Set uniforms
        GLES20.glActiveTexture(GLES20.GL_TEXTURE0)
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, textureId)
        GLES20.glUniform1i(textureHandle, 0)
        GLES20.glUniform1f(alphaHandle, alpha)
        
        // Draw quad
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4)
        
        // Cleanup
        GLES20.glDisableVertexAttribArray(positionHandle)
        GLES20.glDisableVertexAttribArray(textureCoordHandle)
        GLES20.glDisable(GLES20.GL_BLEND)
    }

    private fun createShaderProgram(vertexShaderCode: String, fragmentShaderCode: String): Int {
        val vertexShader = loadShader(GLES20.GL_VERTEX_SHADER, vertexShaderCode)
        val fragmentShader = loadShader(GLES20.GL_FRAGMENT_SHADER, fragmentShaderCode)
        
        if (vertexShader == 0 || fragmentShader == 0) {
            return 0
        }
        
        val program = GLES20.glCreateProgram()
        GLES20.glAttachShader(program, vertexShader)
        GLES20.glAttachShader(program, fragmentShader)
        GLES20.glLinkProgram(program)
        
        val linkStatus = IntArray(1)
        GLES20.glGetProgramiv(program, GLES20.GL_LINK_STATUS, linkStatus, 0)
        if (linkStatus[0] == 0) {
            Log.e(TAG, "Error linking program: ${GLES20.glGetProgramInfoLog(program)}")
            GLES20.glDeleteProgram(program)
            return 0
        }
        
        return program
    }

    private fun loadShader(type: Int, shaderCode: String): Int {
        val shader = GLES20.glCreateShader(type)
        GLES20.glShaderSource(shader, shaderCode)
        GLES20.glCompileShader(shader)
        
        val compileStatus = IntArray(1)
        GLES20.glGetShaderiv(shader, GLES20.GL_COMPILE_STATUS, compileStatus, 0)
        if (compileStatus[0] == 0) {
            Log.e(TAG, "Error compiling shader: ${GLES20.glGetShaderInfoLog(shader)}")
            GLES20.glDeleteShader(shader)
            return 0
        }
        
        return shader
    }

    /**
     * Clean up OpenGL resources
     */
    fun release() {
        if (textureId != 0) {
            GLES20.glDeleteTextures(1, intArrayOf(textureId), 0)
            textureId = 0
        }
        
        if (shaderProgram != 0) {
            GLES20.glDeleteProgram(shaderProgram)
            shaderProgram = 0
        }
        
        vertexBuffer = null
        textureBuffer = null
        locationViewBitmap = null
    }
}
