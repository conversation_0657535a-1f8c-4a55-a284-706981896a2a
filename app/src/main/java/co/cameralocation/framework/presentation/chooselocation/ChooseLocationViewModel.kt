package co.cameralocation.framework.presentation.chooselocation

import dagger.hilt.android.lifecycle.HiltViewModel
import co.cameralocation.framework.network.weather.WeatherResponse
import co.cameralocation.framework.presentation.common.BaseViewModel
import co.cameralocation.framework.presentation.common.launchIO
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfoWithLayoutItem
import co.cameralocation.framework.repository.LocationInfoWithLayoutItemDirectRepository
import co.cameralocation.framework.repository.WeatherRepository
import timber.log.Timber
import javax.inject.Inject
import co.cameralocation.framework.repository.Result

@HiltViewModel
class ChooseLocationViewModel @Inject constructor(
    private val locationInfoWithLayoutItemDirectRepository: LocationInfoWithLayoutItemDirectRepository,
    private val weatherRepository: WeatherRepository,
) : BaseViewModel() {
    fun getLocationInfoWithLayoutItemById(
        locationInfoId: Long?,
        onSuccess: (LocationInfoWithLayoutItem?) -> Unit,
        onError: () -> Unit
    ) {
        launchIO {
            if (locationInfoId == null) {
                onError()
                return@launchIO
            }

            val locationInfoWithLayoutItem = locationInfoWithLayoutItemDirectRepository.getLocationInfoWithLayoutItemById(locationInfoId)
            onSuccess(locationInfoWithLayoutItem)
        }
    }

    fun saveNewLocationToDatabase(
        locationInfoWithLayoutItem: LocationInfoWithLayoutItem,
        onComplete: () -> Unit
    ) {
        launchIO {
            Timber.d("saveNewLocationToDatabase: save new location")
            locationInfoWithLayoutItemDirectRepository.saveLocationInfoWithLayoutItem(locationInfoWithLayoutItem)
            onComplete()
        }
    }

    fun updateLocationToDatabase(
        locationInfoWithLayoutItem: LocationInfoWithLayoutItem? = null,
        onSuccess: () -> Unit,
        onError: () -> Unit
    ) {
        launchIO {
            if (locationInfoWithLayoutItem == null) {
                onError()
                return@launchIO
            }

            Timber.d("updateLocationToDatabase: update location")
            locationInfoWithLayoutItemDirectRepository.updateLocationInfoWithLayoutItem(locationInfoWithLayoutItem)
            onSuccess()
        }
    }

    fun getCurrentWeatherByCoordinates(
        latitude: Double,
        longitude: Double,
        language: String = "en",
        onSuccess: (WeatherResponse) -> Unit,
        onError: () -> Unit
    ) {
        launchIO {
            val result = weatherRepository.getWeatherByCoordinates(
                latitude = latitude,
                longitude = longitude,
                lang = language
            )

            when (result) {
                is Result.Success -> {
                    onSuccess(result.data)
                }
                is Result.Error -> {
                    onError()
                }
            }
        }
    }
}
