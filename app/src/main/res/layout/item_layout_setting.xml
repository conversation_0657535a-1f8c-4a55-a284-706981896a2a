<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_16dp"
        android:background="@drawable/bg_layout_item_type_edit_layout_screen_unselected"
        tools:background="@color/black_121825"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginVertical="@dimen/_8dp">

        <TextView
            android:id="@+id/titleTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            tools:text="Show title"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_16sp"
            android:fontFamily="@font/font_700"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/switchSetting" />

        <com.github.angads25.toggle.widget.LabeledSwitch
            android:id="@+id/switchSetting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:on="true"
             />

        <LinearLayout
            android:id="@+id/editTextLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/_8dp"
            app:layout_constraintTop_toBottomOf="@id/titleTextView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <EditText
                android:id="@+id/editText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/edit_title_here"
                android:textColorHint="#80FFFFFF"
                android:textSize="@dimen/_16sp"
                android:fontFamily="@font/font_400"
                android:layout_marginVertical="@dimen/_16dp"
                android:textColor="@android:color/white"
                android:background="@drawable/item_background_edit_text_title"
                android:padding="@dimen/_8dp" />

            <Button
                android:id="@+id/btnSave"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/save"
                android:fontFamily="@font/font_700"
                android:layout_gravity="center"
                android:textColor="@android:color/white"
                android:backgroundTint="@color/blue_3e9df4"
                android:layout_marginStart="@dimen/_8dp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
