<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_121825">

        <!-- Toolbar -->
        <FrameLayout
            android:id="@+id/toolbarContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent">

            <!-- Default Toolbar -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/toolbarDefault"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/btnBack"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:padding="@dimen/_8dp"
                    android:src="@drawable/ic_back"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:orientation="vertical"
                    android:padding="@dimen/_8dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/btnBack"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tvLocationName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/_16sp"
                        android:textStyle="bold"
                        tools:text="New York, USA" />

                    <TextView
                        android:id="@+id/tvTimestamp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/_12sp"
                        tools:text="01/01/2023 12:30" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Text Editing Toolbar -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/toolbarTextEdit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible"
                android:paddingVertical="10dp">

                <TextView
                    android:id="@+id/btnCancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/cancel"
                    android:textColor="@android:color/white"
                    android:paddingHorizontal="@dimen/_12dp"
                    android:fontFamily="@font/font_600"
                    android:paddingVertical="@dimen/_6dp"
                    android:textSize="@dimen/_14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvTextEditTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/cut"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_18sp"
                    android:fontFamily="@font/font_700"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/btnSave"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/done"
                    android:background="@drawable/bg_radius_8"
                    android:backgroundTint="@color/blue_28abf5"
                    android:textColor="@android:color/white"
                    android:paddingHorizontal="@dimen/_12dp"
                    android:fontFamily="@font/font_600"
                    android:paddingVertical="@dimen/_6dp"
                    android:textSize="@dimen/_14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </FrameLayout>

        <!-- Video Player -->
        <androidx.media3.ui.PlayerView
            android:id="@+id/playerView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@id/videoControls"
            app:layout_constraintTop_toBottomOf="@id/toolbarContainer"
            app:use_controller="false"
            app:show_buffering="when_playing"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:resize_mode="fit" />

        <!-- Video Controls -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/videoControls"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/_8dp"
            app:layout_constraintBottom_toTopOf="@id/flCutControls"
            android:layout_marginBottom="@dimen/_16dp">

            <ImageView
                android:id="@+id/btnPlayPause"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/_6dp"
                android:clickable="true"
                android:focusable="true"
                android:src="@drawable/ic_resume_video_recording_camera_screen"
                app:tint="@android:color/white"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />

            <TextView
                android:id="@+id/tvCurrentTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_12sp"
                android:fontFamily="@font/font_400"
                android:layout_marginStart="@dimen/_4dp"
                app:layout_constraintBottom_toBottomOf="@id/btnPlayPause"
                app:layout_constraintStart_toEndOf="@id/btnPlayPause"
                app:layout_constraintTop_toTopOf="@id/btnPlayPause"
                tools:text="00:00" />

            <SeekBar
                android:id="@+id/seekBarVideo"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_8dp"
                android:max="100"
                android:progress="0"
                app:layout_constraintEnd_toStartOf="@id/tvTotalTime"
                app:layout_constraintStart_toEndOf="@id/tvCurrentTime"
                app:layout_constraintTop_toTopOf="@id/tvCurrentTime" />

            <TextView
                android:id="@+id/tvTotalTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_12sp"
                android:fontFamily="@font/font_400"
                app:layout_constraintBottom_toBottomOf="@id/seekBarVideo"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/seekBarVideo"
                tools:text="02:30" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Cut Controls -->
        <FrameLayout
            android:id="@+id/flCutControls"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/bottomActions"
            app:layout_constraintDimensionRatio="H,312:48"
            android:layout_marginHorizontal="@dimen/_24dp"
            android:layout_marginVertical="@dimen/_16dp">

            <co.cameralocation.customview.VideoTimeView
                android:id="@+id/videoTimeView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"/>
        </FrameLayout>

        <!-- Bottom Actions -->
        <LinearLayout
            android:id="@+id/bottomActions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="@dimen/_8dp"
            app:layout_constraintBottom_toTopOf="@id/layoutAds">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/btnCutVideo"
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:src="@drawable/ic_video_editor_camera_gps"
                    app:tint="@android:color/white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/cut_video"
                    android:fontFamily="@font/font_600"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_12sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/btnShare"
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:src="@drawable/ic_share"
                    app:tint="@android:color/white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/share"
                    android:fontFamily="@font/font_600"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_12sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/btnDelete"
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:src="@drawable/ic_delete_edit_media_screen"
                    app:tint="@android:color/white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/delete"
                    android:fontFamily="@font/font_600"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_12sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/btnSaveAs"
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:src="@drawable/ic_save_as_edit_media_screen"
                    app:tint="@android:color/white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/save_as"
                    android:fontFamily="@font/font_600"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_12sp" />

            </LinearLayout>

        </LinearLayout>

        <FrameLayout
            android:id="@+id/layoutAds"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="360:70">

            <FrameLayout
                android:id="@+id/adViewGroup"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="bottom"
                android:background="#D7D6D6">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fontFamily="@font/font_400"
                    android:gravity="center"
                    android:text="@string/loading_ads"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16sp" />

            </FrameLayout>
        </FrameLayout>

        <!-- Loading Progress -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
